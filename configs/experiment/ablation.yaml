# @package _global_

# 消融实验配置 - 测试不同的KL权重
defaults:
  - override /model: sd15
  - override /training: default
  - override /data: default

experiment_name: ablation_kl_weight

# 实验特定的覆盖
training:
  num_epochs: 15
  batch_size: 4

model:
  loss_weights:
    reconstruction: 1.0
    kl_divergence: 0.05  # 减少KL权重

logging:
  use_aim: true
  log_every_n_steps: 5

# 可以通过命令行覆盖的参数
# python train.py experiment=ablation model.loss_weights.kl_divergence=0.2
