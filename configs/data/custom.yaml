# @package data

name: custom

# 数据路径 - 需要用户指定
train_data_dir: ???  # 必须指定
val_data_dir: ???    # 必须指定

# 图像预处理 - 自定义尺寸
image_size: 768
center_crop: false
random_flip: false

# 数据增强 - 更强的增强
augmentation:
  enabled: true
  brightness: 0.2
  contrast: 0.2
  saturation: 0.2
  hue: 0.1
  rotation: 10
  gaussian_blur: 0.1

# 数据集设置
cache_latents: true
validation_split: 0.15

# 支持的图像格式
valid_extensions:
  - .jpg
  - .jpeg
  - .png
  - .bmp
  - .tiff
  - .webp
