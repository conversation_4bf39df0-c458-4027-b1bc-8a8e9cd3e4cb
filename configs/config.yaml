# @package _global_

# 默认配置组
defaults:
  - model: sd15
  - training: default
  - data: default
  - experiment: baseline
  - _self_

# 基础设置
project_name: vae_tuner
experiment_name: ${model.name}_${training.name}_${data.name}
output_dir: ./outputs/${experiment_name}

# 随机种子
seed: 42

# 设备设置
device: auto  # auto, cpu, cuda, mps
mixed_precision: fp16  # no, fp16, bf16

# 日志设置
logging:
  level: INFO
  use_aim: true
  aim_repo: ./aim_logs
  log_every_n_steps: 10
  save_every_n_epochs: 5

# 早停设置
early_stopping:
  patience: 5
  monitor: val_loss
  mode: min

# Hydra设置
hydra:
  run:
    dir: ./outputs/${experiment_name}/${now:%Y-%m-%d_%H-%M-%S}
  sweep:
    dir: ./outputs/multirun/${now:%Y-%m-%d_%H-%M-%S}
    subdir: ${hydra:job.num}
