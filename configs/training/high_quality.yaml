# @package training

name: high_quality

# 高质量训练配置 - 更多epoch和更小学习率
num_epochs: 50
batch_size: 2
gradient_accumulation_steps: 2

# 优化器参数
optimizer:
  name: adamw
  learning_rate: 5e-6
  weight_decay: 0.02
  betas: [0.9, 0.999]
  eps: 1e-8

# 学习率调度器
lr_scheduler:
  name: cosine
  warmup_steps: 500
  warmup_ratio: 0.05
  num_cycles: 1

# 梯度相关
max_grad_norm: 0.5
gradient_checkpointing: true

# 数据加载
num_workers: 4
pin_memory: true
persistent_workers: true
