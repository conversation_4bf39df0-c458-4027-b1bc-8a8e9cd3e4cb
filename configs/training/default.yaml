# @package training

name: default

# 基础训练参数
num_epochs: 20
batch_size: 4
gradient_accumulation_steps: 1

# 优化器参数
optimizer:
  name: adamw
  learning_rate: 1e-5
  weight_decay: 0.01
  betas: [0.9, 0.999]
  eps: 1e-8

# 学习率调度器
lr_scheduler:
  name: cosine
  warmup_steps: 0
  warmup_ratio: 0.0
  num_cycles: 1

# 梯度相关
max_grad_norm: 1.0
gradient_checkpointing: false

# 数据加载
num_workers: 4
pin_memory: true
persistent_workers: true
