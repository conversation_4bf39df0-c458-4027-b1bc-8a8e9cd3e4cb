# @package training

name: fast

# 快速训练配置 - 用于调试和快速验证
num_epochs: 5
batch_size: 8
gradient_accumulation_steps: 1

# 优化器参数
optimizer:
  name: adamw
  learning_rate: 2e-5
  weight_decay: 0.01
  betas: [0.9, 0.999]
  eps: 1e-8

# 学习率调度器
lr_scheduler:
  name: linear
  warmup_steps: 100
  warmup_ratio: 0.1
  num_cycles: 1

# 梯度相关
max_grad_norm: 1.0
gradient_checkpointing: true

# 数据加载
num_workers: 2
pin_memory: true
persistent_workers: false
