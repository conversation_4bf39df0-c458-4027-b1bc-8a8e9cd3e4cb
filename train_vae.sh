#!/bin/bash

# 单GPU训练
accelerate launch --mixed_precision=fp16 vae_tuner.py \
    --model_name "runwayml/stable-diffusion-v1-5" \
    --train_data_dir "/path/to/train/images" \
    --val_data_dir "/path/to/val/images" \
    --output_dir "./vae_finetuned" \
    --batch_size 4 \
    --num_epochs 20 \
    --learning_rate 1e-5 \
    --weight_decay 0.01 \
    --gradient_accumulation_steps 2 \
    --mixed_precision fp16 \
    --patience 5 \
    --use_aim \
    --experiment_name "vae_finetune_v1"

# 多GPU训练
# accelerate launch --multi_gpu --num_processes=2 --mixed_precision=fp16 vae_tuner.py \
#     --model_name "runwayml/stable-diffusion-v1-5" \
#     --train_data_dir "/path/to/train/images" \
#     --val_data_dir "/path/to/val/images" \
#     --output_dir "./vae_finetuned" \
#     --batch_size 8 \
#     --num_epochs 20 \
#     --learning_rate 1e-5 \
#     --weight_decay 0.01 \
#     --gradient_accumulation_steps 1 \
#     --mixed_precision fp16 \
#     --patience 5 \
#     --use_aim \
#     --experiment_name "vae_finetune_multi_gpu"