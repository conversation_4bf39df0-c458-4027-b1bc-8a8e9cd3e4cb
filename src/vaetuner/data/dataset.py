"""Dataset classes and data utilities."""

import torch
from torch.utils.data import Dataset
from torchvision import transforms
from PIL import Image
from pathlib import Path
from omegaconf import DictConfig
from typing import Optional, List


class ImageDataset(Dataset):
    """Dataset for loading images for VAE training."""
    
    def __init__(self, image_dir: str, transform: Optional[transforms.Compose] = None, 
                 valid_extensions: Optional[List[str]] = None):
        """Initialize image dataset.
        
        Args:
            image_dir: Directory containing images
            transform: Image transformations to apply
            valid_extensions: List of valid image file extensions
        """
        self.image_dir = Path(image_dir)
        self.transform = transform
        self.image_paths = []
        
        # Default valid extensions
        if valid_extensions is None:
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        # Find all valid image files
        for ext in valid_extensions:
            self.image_paths.extend(list(self.image_dir.glob(f"**/*{ext}")))
            self.image_paths.extend(list(self.image_dir.glob(f"**/*{ext.upper()}")))
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image


def create_transforms(config: DictConfig) -> transforms.Compose:
    """Create image transforms based on configuration.
    
    Args:
        config: Data configuration
        
    Returns:
        Composed transforms
    """
    transform_list = []
    
    # Resize
    if config.center_crop:
        transform_list.append(transforms.CenterCrop(config.image_size))
    else:
        transform_list.append(transforms.Resize((config.image_size, config.image_size)))
    
    # Data augmentation
    if config.augmentation.enabled:
        if config.random_flip:
            transform_list.append(transforms.RandomHorizontalFlip(p=0.5))
        
        if hasattr(config.augmentation, 'rotation') and config.augmentation.rotation > 0:
            transform_list.append(
                transforms.RandomRotation(degrees=config.augmentation.rotation)
            )
        
        # Color jitter
        if any(hasattr(config.augmentation, attr) and getattr(config.augmentation, attr) > 0 
               for attr in ['brightness', 'contrast', 'saturation', 'hue']):
            transform_list.append(
                transforms.ColorJitter(
                    brightness=getattr(config.augmentation, 'brightness', 0),
                    contrast=getattr(config.augmentation, 'contrast', 0),
                    saturation=getattr(config.augmentation, 'saturation', 0),
                    hue=getattr(config.augmentation, 'hue', 0)
                )
            )
        
        # Gaussian blur
        if hasattr(config.augmentation, 'gaussian_blur') and config.augmentation.gaussian_blur > 0:
            transform_list.append(
                transforms.RandomApply([
                    transforms.GaussianBlur(kernel_size=3, sigma=(0.1, config.augmentation.gaussian_blur))
                ], p=0.3)
            )
    
    # Convert to tensor and normalize
    transform_list.extend([
        transforms.ToTensor(),
        transforms.Normalize([0.5], [0.5])  # [-1, 1] range
    ])
    
    return transforms.Compose(transform_list)
