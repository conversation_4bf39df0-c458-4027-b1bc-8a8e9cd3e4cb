"""VAE Tuner model class."""

import torch
import torch.nn.functional as F
from diffusers import AutoencoderKL
from omegaconf import DictConfig


class VAETuner:
    """VAE fine-tuning model wrapper."""
    
    def __init__(self, config: DictConfig):
        """Initialize VAE Tuner.
        
        Args:
            config: Model configuration from Hydra
        """
        self.config = config
        self.vae = None
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def load_model(self):
        """Load the VAE model."""
        self.vae = AutoencoderKL.from_pretrained(
            self.config.model_name,
            subfolder=self.config.subfolder
        )
        return self.vae
    
    def compute_vae_loss(self, images):
        """Compute VAE loss including reconstruction and KL divergence.
        
        Args:
            images: Input images tensor
            
        Returns:
            tuple: (total_loss, reconstruction_loss, kl_loss)
        """
        # Encode
        posterior = self.vae.encode(images).latent_dist
        z = posterior.sample()
        
        # Decode
        reconstructed = self.vae.decode(z).sample
        
        # Reconstruction loss
        recon_loss = F.mse_loss(reconstructed, images, reduction='mean')
        
        # KL divergence loss
        kl_loss = posterior.kl().mean()
        
        # Total loss with configurable weights
        total_loss = (
            self.config.loss_weights.reconstruction * recon_loss +
            self.config.loss_weights.kl_divergence * kl_loss
        )
        
        return total_loss, recon_loss, kl_loss
    
    def enable_gradient_checkpointing(self):
        """Enable gradient checkpointing for memory efficiency."""
        if hasattr(self.vae, 'enable_gradient_checkpointing'):
            self.vae.enable_gradient_checkpointing()
    
    def enable_xformers(self):
        """Enable xFormers memory efficient attention."""
        if hasattr(self.vae, 'enable_xformers_memory_efficient_attention'):
            try:
                self.vae.enable_xformers_memory_efficient_attention()
            except Exception as e:
                print(f"Could not enable xFormers: {e}")
