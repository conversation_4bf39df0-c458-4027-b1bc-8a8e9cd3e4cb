"""Training logic for VAE fine-tuning."""

import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
from pathlib import Path
from omegaconf import DictConfig
from accelerate import Accelerator
from aim import Run
import logging

from ..models.vae_tuner import VAETuner
from ..utils.logging import setup_logging


class Trainer:
    """Main trainer class for VAE fine-tuning."""
    
    def __init__(self, config: DictConfig, accelerator: Accelerator):
        """Initialize trainer.
        
        Args:
            config: Full configuration from Hydra
            accelerator: Accelerate accelerator instance
        """
        self.config = config
        self.accelerator = accelerator
        self.logger = setup_logging(config.logging.level)
        
        # Initialize VAE tuner
        self.vae_tuner = VAETuner(config.model)
        
        # Initialize Aim logging
        self.aim_run = None
        if config.logging.use_aim and accelerator.is_main_process:
            self.aim_run = Run(
                experiment=config.experiment_name,
                repo=config.logging.aim_repo
            )
        
        # Training state
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def setup_model(self):
        """Setup the VAE model with optimizations."""
        vae = self.vae_tuner.load_model()
        
        # Enable optimizations based on config
        if self.config.model.gradient_checkpointing:
            self.vae_tuner.enable_gradient_checkpointing()
            
        if self.config.model.enable_xformers:
            self.vae_tuner.enable_xformers()
            
        return vae
    
    def setup_optimizer_and_scheduler(self, vae, train_dataloader):
        """Setup optimizer and learning rate scheduler.
        
        Args:
            vae: VAE model
            train_dataloader: Training data loader
            
        Returns:
            tuple: (optimizer, lr_scheduler)
        """
        # Optimizer
        optimizer_config = self.config.training.optimizer
        
        if optimizer_config.name.lower() == 'adamw':
            optimizer = torch.optim.AdamW(
                vae.parameters(),
                lr=optimizer_config.learning_rate,
                weight_decay=optimizer_config.weight_decay,
                betas=optimizer_config.betas,
                eps=optimizer_config.eps
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_config.name}")
        
        # Learning rate scheduler
        scheduler_config = self.config.training.lr_scheduler
        total_steps = self.config.training.num_epochs * len(train_dataloader)
        
        if scheduler_config.name.lower() == 'cosine':
            lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, 
                T_max=total_steps,
                eta_min=optimizer_config.learning_rate * 0.01
            )
        elif scheduler_config.name.lower() == 'linear':
            from transformers import get_linear_schedule_with_warmup
            lr_scheduler = get_linear_schedule_with_warmup(
                optimizer,
                num_warmup_steps=scheduler_config.warmup_steps,
                num_training_steps=total_steps
            )
        else:
            raise ValueError(f"Unsupported scheduler: {scheduler_config.name}")
        
        return optimizer, lr_scheduler

    def train_epoch(self, train_dataloader, optimizer, lr_scheduler, epoch):
        """Train for one epoch.

        Args:
            train_dataloader: Training data loader
            optimizer: Optimizer
            lr_scheduler: Learning rate scheduler
            epoch: Current epoch number

        Returns:
            tuple: (avg_loss, avg_recon_loss, avg_kl_loss)
        """
        self.vae_tuner.vae.train()
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0

        progress_bar = tqdm(
            train_dataloader,
            desc=f"Epoch {epoch}",
            disable=not self.accelerator.is_local_main_process
        )

        for step, batch in enumerate(progress_bar):
            with self.accelerator.accumulate(self.vae_tuner.vae):
                # Compute loss
                loss, recon_loss, kl_loss = self.vae_tuner.compute_vae_loss(batch)

                # Backward pass
                self.accelerator.backward(loss)

                # Gradient clipping
                if self.accelerator.sync_gradients:
                    self.accelerator.clip_grad_norm_(
                        self.vae_tuner.vae.parameters(),
                        self.config.training.max_grad_norm
                    )

                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()

            # Accumulate losses
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()

            # Update progress bar
            if step % self.config.logging.log_every_n_steps == 0:
                progress_bar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'recon': f'{recon_loss.item():.4f}',
                    'kl': f'{kl_loss.item():.4f}',
                    'lr': f'{lr_scheduler.get_last_lr()[0]:.2e}'
                })

        avg_loss = total_loss / len(train_dataloader)
        avg_recon_loss = total_recon_loss / len(train_dataloader)
        avg_kl_loss = total_kl_loss / len(train_dataloader)

        return avg_loss, avg_recon_loss, avg_kl_loss

    def validate(self, val_dataloader):
        """Validate the model.

        Args:
            val_dataloader: Validation data loader

        Returns:
            tuple: (avg_loss, avg_recon_loss, avg_kl_loss)
        """
        self.vae_tuner.vae.eval()
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0

        with torch.no_grad():
            for batch in tqdm(
                val_dataloader,
                desc="Validating",
                disable=not self.accelerator.is_local_main_process
            ):
                loss, recon_loss, kl_loss = self.vae_tuner.compute_vae_loss(batch)

                total_loss += loss.item()
                total_recon_loss += recon_loss.item()
                total_kl_loss += kl_loss.item()

        avg_loss = total_loss / len(val_dataloader)
        avg_recon_loss = total_recon_loss / len(val_dataloader)
        avg_kl_loss = total_kl_loss / len(val_dataloader)

        return avg_loss, avg_recon_loss, avg_kl_loss

    def log_metrics(self, metrics, step):
        """Log metrics to Aim and console.

        Args:
            metrics: Dictionary of metrics to log
            step: Current step/epoch
        """
        if self.aim_run and self.accelerator.is_main_process:
            for key, value in metrics.items():
                self.aim_run.track(value, name=key, step=step)

    def save_model(self, save_path, is_best=False):
        """Save the model.

        Args:
            save_path: Path to save the model
            is_best: Whether this is the best model so far
        """
        if self.accelerator.is_main_process:
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)

            # Save VAE
            unwrapped_vae = self.accelerator.unwrap_model(self.vae_tuner.vae)
            unwrapped_vae.save_pretrained(save_dir / "vae")

            if is_best:
                unwrapped_vae.save_pretrained(save_dir / "best_vae")
                self.logger.info(f"Best model saved to {save_dir / 'best_vae'}")

    def should_stop_early(self, val_loss):
        """Check if training should stop early.

        Args:
            val_loss: Current validation loss

        Returns:
            tuple: (should_stop, is_best)
        """
        is_best = val_loss < self.best_val_loss

        if is_best:
            self.best_val_loss = val_loss
            self.patience_counter = 0
        else:
            self.patience_counter += 1

        should_stop = self.patience_counter >= self.config.early_stopping.patience

        return should_stop, is_best

    def train(self, train_dataloader, val_dataloader):
        """Main training loop.

        Args:
            train_dataloader: Training data loader
            val_dataloader: Validation data loader
        """
        # Setup model
        vae = self.setup_model()

        # Setup optimizer and scheduler
        optimizer, lr_scheduler = self.setup_optimizer_and_scheduler(vae, train_dataloader)

        # Prepare with accelerate
        vae, optimizer, train_dataloader, val_dataloader, lr_scheduler = self.accelerator.prepare(
            vae, optimizer, train_dataloader, val_dataloader, lr_scheduler
        )

        # Update vae reference in tuner
        self.vae_tuner.vae = vae

        # Log hyperparameters
        if self.aim_run:
            self.aim_run["hparams"] = {
                "learning_rate": self.config.training.optimizer.learning_rate,
                "batch_size": self.config.training.batch_size,
                "num_epochs": self.config.training.num_epochs,
                "weight_decay": self.config.training.optimizer.weight_decay,
                "gradient_accumulation_steps": self.config.training.gradient_accumulation_steps,
                "mixed_precision": self.config.mixed_precision,
                "model_name": self.config.model.model_name,
            }

        # Training loop
        for epoch in range(self.config.training.num_epochs):
            # Train
            train_loss, train_recon, train_kl = self.train_epoch(
                train_dataloader, optimizer, lr_scheduler, epoch + 1
            )

            # Validate
            val_loss, val_recon, val_kl = self.validate(val_dataloader)

            # Log metrics
            metrics = {
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_recon_loss': train_recon,
                'val_recon_loss': val_recon,
                'train_kl_loss': train_kl,
                'val_kl_loss': val_kl,
                'learning_rate': lr_scheduler.get_last_lr()[0]
            }

            self.log_metrics(metrics, epoch + 1)

            # Print progress
            if self.accelerator.is_main_process:
                self.logger.info(
                    f"Epoch {epoch+1}/{self.config.training.num_epochs} - "
                    f"Train Loss: {train_loss:.4f} - Val Loss: {val_loss:.4f} - "
                    f"LR: {lr_scheduler.get_last_lr()[0]:.2e}"
                )
                self.logger.info(f"Val Recon: {val_recon:.4f} - Val KL: {val_kl:.4f}")

            # Early stopping and model saving
            should_stop, is_best = self.should_stop_early(val_loss)

            if is_best:
                self.save_model(self.config.output_dir, is_best=True)

            # Regular saving
            if (epoch + 1) % self.config.logging.save_every_n_epochs == 0:
                self.save_model(self.config.output_dir)

            # Early stopping
            if should_stop:
                self.logger.info(
                    f"Early stopping after {self.config.early_stopping.patience} "
                    f"epochs without improvement"
                )
                break

        # Final save
        self.save_model(self.config.output_dir)

        # Close Aim run
        if self.aim_run:
            self.aim_run.close()
