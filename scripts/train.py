#!/usr/bin/env python3
"""Training script for VAE fine-tuning with Hydra configuration."""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import hydra
from omegaconf import DictConfig, OmegaConf
from torch.utils.data import DataLoader
from accelerate import Accelerator

from vaetuner.data.dataset import ImageDataset, create_transforms
from vaetuner.training.trainer import Trainer


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def main(cfg: DictConfig) -> None:
    """Main training function.
    
    Args:
        cfg: Hydra configuration
    """
    # Print configuration
    print("Configuration:")
    print(OmegaConf.to_yaml(cfg))
    
    # Initialize Accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=cfg.training.gradient_accumulation_steps,
        mixed_precision=cfg.mixed_precision,
    )
    
    # Set seed
    if hasattr(cfg, 'seed'):
        import torch
        torch.manual_seed(cfg.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(cfg.seed)
    
    # Create transforms
    transform = create_transforms(cfg.data)
    
    # Create datasets
    train_dataset = ImageDataset(
        cfg.data.train_data_dir,
        transform=transform,
        valid_extensions=cfg.data.valid_extensions
    )
    
    val_dataset = ImageDataset(
        cfg.data.val_data_dir,
        transform=transform,
        valid_extensions=cfg.data.valid_extensions
    )
    
    # Create data loaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=cfg.training.batch_size,
        shuffle=True,
        num_workers=cfg.training.num_workers,
        pin_memory=cfg.training.pin_memory,
        persistent_workers=cfg.training.persistent_workers and cfg.training.num_workers > 0
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=cfg.training.batch_size,
        shuffle=False,
        num_workers=cfg.training.num_workers,
        pin_memory=cfg.training.pin_memory,
        persistent_workers=cfg.training.persistent_workers and cfg.training.num_workers > 0
    )
    
    accelerator.print(f"Train dataset size: {len(train_dataset)}")
    accelerator.print(f"Validation dataset size: {len(val_dataset)}")
    
    # Initialize trainer
    trainer = Trainer(cfg, accelerator)
    
    # Start training
    trainer.train(train_dataloader, val_dataloader)
    
    accelerator.print("Training completed!")


if __name__ == "__main__":
    main()
