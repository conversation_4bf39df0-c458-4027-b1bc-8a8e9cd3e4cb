#!/usr/bin/env python3
"""Migration script to help convert old command-line arguments to new Hydra configuration."""

import argparse
import sys
from pathlib import Path


def convert_args_to_hydra(args):
    """Convert old command-line arguments to Hydra overrides."""
    hydra_overrides = []
    
    # Data arguments
    if args.train_data_dir:
        hydra_overrides.append(f"data.train_data_dir={args.train_data_dir}")
    if args.val_data_dir:
        hydra_overrides.append(f"data.val_data_dir={args.val_data_dir}")
    if args.image_size:
        hydra_overrides.append(f"data.image_size={args.image_size}")
    
    # Model arguments
    if args.model_name:
        hydra_overrides.append(f"model.model_name={args.model_name}")
    
    # Training arguments
    if args.batch_size:
        hydra_overrides.append(f"training.batch_size={args.batch_size}")
    if args.num_epochs:
        hydra_overrides.append(f"training.num_epochs={args.num_epochs}")
    if args.learning_rate:
        hydra_overrides.append(f"training.optimizer.learning_rate={args.learning_rate}")
    if args.weight_decay:
        hydra_overrides.append(f"training.optimizer.weight_decay={args.weight_decay}")
    if args.gradient_accumulation_steps:
        hydra_overrides.append(f"training.gradient_accumulation_steps={args.gradient_accumulation_steps}")
    
    # Output and experiment
    if args.output_dir:
        hydra_overrides.append(f"output_dir={args.output_dir}")
    if args.experiment_name:
        hydra_overrides.append(f"experiment_name={args.experiment_name}")
    
    # Logging
    if hasattr(args, 'use_aim') and args.use_aim:
        hydra_overrides.append("logging.use_aim=true")
    if args.aim_repo:
        hydra_overrides.append(f"logging.aim_repo={args.aim_repo}")
    
    # Early stopping
    if args.patience:
        hydra_overrides.append(f"early_stopping.patience={args.patience}")
    
    # Mixed precision
    if args.mixed_precision:
        hydra_overrides.append(f"mixed_precision={args.mixed_precision}")
    
    return hydra_overrides


def main():
    parser = argparse.ArgumentParser(description="Convert old VAE tuner arguments to new Hydra format")
    
    # Old arguments (same as original vae_tuner.py)
    parser.add_argument("--model_name", type=str, default="runwayml/stable-diffusion-v1-5")
    parser.add_argument("--train_data_dir", type=str, required=True)
    parser.add_argument("--val_data_dir", type=str, required=True)
    parser.add_argument("--output_dir", type=str, default="./vae_finetuned")
    parser.add_argument("--batch_size", type=int, default=4)
    parser.add_argument("--num_epochs", type=int, default=20)
    parser.add_argument("--learning_rate", type=float, default=1e-5)
    parser.add_argument("--weight_decay", type=float, default=0.01)
    parser.add_argument("--image_size", type=int, default=512)
    parser.add_argument("--mixed_precision", type=str, default="fp16")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)
    parser.add_argument("--patience", type=int, default=5)
    parser.add_argument("--use_aim", action="store_true")
    parser.add_argument("--experiment_name", type=str, default="vae_finetune")
    parser.add_argument("--aim_repo", type=str, default=".")
    
    args = parser.parse_args()
    
    # Convert to Hydra overrides
    hydra_overrides = convert_args_to_hydra(args)
    
    # Generate the new command
    new_command = "python scripts/train.py " + " ".join(hydra_overrides)
    
    print("Old command line arguments converted to new Hydra format:")
    print()
    print(new_command)
    print()
    print("You can also save this as a shell script for future use:")
    print()
    print("#!/bin/bash")
    print(new_command)
    
    # Optionally save to file
    save_file = input("\nSave this command to a shell script? (y/n): ").lower().strip()
    if save_file == 'y':
        filename = input("Enter filename (default: train_migrated.sh): ").strip()
        if not filename:
            filename = "train_migrated.sh"
        
        with open(filename, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write(f"# Migrated from old vae_tuner.py arguments\n\n")
            f.write(new_command + "\n")
        
        print(f"Command saved to {filename}")
        print(f"Make it executable with: chmod +x {filename}")


if __name__ == "__main__":
    main()
