#!/usr/bin/env python3
"""Inference script for testing fine-tuned VAE."""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
import hydra
from omegaconf import DictConfig
from PIL import Image
from torchvision import transforms
from diffusers import AutoencoderKL
import matplotlib.pyplot as plt


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def main(cfg: DictConfig) -> None:
    """Main inference function.
    
    Args:
        cfg: Hydra configuration
    """
    # Load the fine-tuned VAE
    vae_path = Path(cfg.output_dir) / "best_vae"
    if not vae_path.exists():
        vae_path = Path(cfg.output_dir) / "vae"
    
    if not vae_path.exists():
        print(f"No trained model found at {cfg.output_dir}")
        return
    
    print(f"Loading VAE from {vae_path}")
    vae = AutoencoderKL.from_pretrained(vae_path)
    vae.eval()
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    vae = vae.to(device)
    
    # Create transform
    transform = transforms.Compose([
        transforms.Resize((cfg.data.image_size, cfg.data.image_size)),
        transforms.ToTensor(),
        transforms.Normalize([0.5], [0.5])
    ])
    
    # Test with a sample image (you can modify this path)
    test_image_path = "test_image.jpg"  # Replace with actual test image
    
    if not Path(test_image_path).exists():
        print(f"Test image not found: {test_image_path}")
        print("Please provide a test image path or place a test_image.jpg in the current directory")
        return
    
    # Load and preprocess image
    image = Image.open(test_image_path).convert('RGB')
    original_size = image.size
    
    input_tensor = transform(image).unsqueeze(0).to(device)
    
    # Encode and decode
    with torch.no_grad():
        # Encode
        posterior = vae.encode(input_tensor).latent_dist
        latents = posterior.sample()
        
        # Decode
        reconstructed = vae.decode(latents).sample
    
    # Convert back to PIL image
    reconstructed = (reconstructed / 2 + 0.5).clamp(0, 1)
    reconstructed = reconstructed.cpu().squeeze(0).permute(1, 2, 0).numpy()
    reconstructed_image = Image.fromarray((reconstructed * 255).astype('uint8'))
    
    # Save results
    output_dir = Path("inference_results")
    output_dir.mkdir(exist_ok=True)
    
    # Resize back to original size for comparison
    reconstructed_image = reconstructed_image.resize(original_size, Image.LANCZOS)
    
    # Save images
    original_path = output_dir / "original.jpg"
    reconstructed_path = output_dir / "reconstructed.jpg"
    
    image.save(original_path)
    reconstructed_image.save(reconstructed_path)
    
    print(f"Results saved to {output_dir}")
    print(f"Original: {original_path}")
    print(f"Reconstructed: {reconstructed_path}")
    
    # Create comparison plot
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))
    
    axes[0].imshow(image)
    axes[0].set_title("Original")
    axes[0].axis('off')
    
    axes[1].imshow(reconstructed_image)
    axes[1].set_title("Reconstructed")
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / "comparison.png", dpi=150, bbox_inches='tight')
    print(f"Comparison plot saved to {output_dir / 'comparison.png'}")


if __name__ == "__main__":
    main()
