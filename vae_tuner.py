import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
from PIL import Image
import os
import argparse
from pathlib import Path
from tqdm import tqdm
import logging

from diffusers import AutoencoderK<PERSON>
from accelerate import Accelerator
from aim import Run

class ImageDataset(Dataset):
    def __init__(self, image_dir, transform=None):
        self.image_dir = Path(image_dir)
        self.transform = transform
        self.image_paths = []
        
        # 支持的图片格式
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        for ext in valid_extensions:
            self.image_paths.extend(list(self.image_dir.glob(f"**/*{ext}")))
            self.image_paths.extend(list(self.image_dir.glob(f"**/*{ext.upper()}")))
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image

class VAETuner:
    def __init__(self, model_name, accelerator, use_aim=True, experiment_name="vae_finetune", aim_repo="."):
        self.accelerator = accelerator
        self.use_aim = use_aim
        
        # 加载VAE模型
        self.vae = AutoencoderKL.from_pretrained(model_name, subfolder="vae")
        
        # 初始化Aim
        if use_aim and accelerator.is_main_process:
            self.aim_run = Run(experiment=experiment_name, repo=aim_repo)
        else:
            self.aim_run = None
            
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
    def prepare_data_transforms(self, image_size=512):
        """准备数据预处理"""
        return transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize([0.5], [0.5])  # [-1, 1] range
        ])
    
    def compute_vae_loss(self, images):
        """计算VAE损失"""
        # 编码
        posterior = self.vae.encode(images).latent_dist
        z = posterior.sample()
        
        # 解码
        reconstructed = self.vae.decode(z).sample
        
        # 重建损失
        recon_loss = F.mse_loss(reconstructed, images, reduction='mean')
        
        # KL散度损失
        kl_loss = posterior.kl().mean()
        
        # 总损失
        total_loss = recon_loss + 0.1 * kl_loss  # KL权重可调
        
        return total_loss, recon_loss, kl_loss
    
    def train_epoch(self, train_dataloader, optimizer, lr_scheduler, epoch):
        """训练一个epoch"""
        self.vae.train()
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0
        
        progress_bar = tqdm(
            train_dataloader, 
            desc=f"Epoch {epoch}",
            disable=not self.accelerator.is_local_main_process
        )
        
        for step, batch in enumerate(progress_bar):
            with self.accelerator.accumulate(self.vae):
                # 计算损失
                loss, recon_loss, kl_loss = self.compute_vae_loss(batch)
                
                # 反向传播
                self.accelerator.backward(loss)
                
                # 梯度裁剪
                if self.accelerator.sync_gradients:
                    self.accelerator.clip_grad_norm_(self.vae.parameters(), 1.0)
                
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
            
            # 累积损失
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()
            
            # 更新进度条
            if step % 10 == 0:
                progress_bar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'recon': f'{recon_loss.item():.4f}',
                    'kl': f'{kl_loss.item():.4f}',
                    'lr': f'{lr_scheduler.get_last_lr()[0]:.2e}'
                })
        
        avg_loss = total_loss / len(train_dataloader)
        avg_recon_loss = total_recon_loss / len(train_dataloader)
        avg_kl_loss = total_kl_loss / len(train_dataloader)
        
        return avg_loss, avg_recon_loss, avg_kl_loss
    
    def validate(self, val_dataloader):
        """验证"""
        self.vae.eval()
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0
        
        with torch.no_grad():
            for batch in tqdm(val_dataloader, desc="Validating", disable=not self.accelerator.is_local_main_process):
                loss, recon_loss, kl_loss = self.compute_vae_loss(batch)
                
                total_loss += loss.item()
                total_recon_loss += recon_loss.item()
                total_kl_loss += kl_loss.item()
        
        avg_loss = total_loss / len(val_dataloader)
        avg_recon_loss = total_recon_loss / len(val_dataloader)
        avg_kl_loss = total_kl_loss / len(val_dataloader)
        
        return avg_loss, avg_recon_loss, avg_kl_loss
    
    def log_metrics(self, metrics, step):
        """记录指标到Aim"""
        if self.aim_run and self.accelerator.is_main_process:
            for key, value in metrics.items():
                self.aim_run.track(value, name=key, step=step)
    
    def save_model(self, save_path, is_best=False):
        """保存模型"""
        if self.accelerator.is_main_process:
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存VAE
            unwrapped_vae = self.accelerator.unwrap_model(self.vae)
            unwrapped_vae.save_pretrained(save_dir / "vae")
            
            if is_best:
                unwrapped_vae.save_pretrained(save_dir / "best_vae")
                self.accelerator.print(f"→ Best model saved to {save_dir / 'best_vae'}")
    
    def train(self, train_dataloader, val_dataloader, args):
        """主训练循环"""
        # 设置优化器
        optimizer = torch.optim.AdamW(
            self.vae.parameters(),
            lr=args.learning_rate,
            weight_decay=args.weight_decay
        )
        
        # 学习率调度器
        lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=args.num_epochs * len(train_dataloader)
        )
        
        # Accelerate准备
        self.vae, optimizer, train_dataloader, val_dataloader, lr_scheduler = self.accelerator.prepare(
            self.vae, optimizer, train_dataloader, val_dataloader, lr_scheduler
        )
        
        # 记录超参数
        if self.aim_run:
            self.aim_run["hparams"] = {
                "learning_rate": args.learning_rate,
                "batch_size": args.batch_size,
                "num_epochs": args.num_epochs,
                "weight_decay": args.weight_decay,
                "gradient_accumulation_steps": args.gradient_accumulation_steps,
                "mixed_precision": args.mixed_precision,
            }
        
        # 训练循环
        for epoch in range(args.num_epochs):
            # 训练
            train_loss, train_recon, train_kl = self.train_epoch(
                train_dataloader, optimizer, lr_scheduler, epoch + 1
            )
            
            # 验证
            val_loss, val_recon, val_kl = self.validate(val_dataloader)
            
            # 记录指标
            metrics = {
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_recon_loss': train_recon,
                'val_recon_loss': val_recon,
                'train_kl_loss': train_kl,
                'val_kl_loss': val_kl,
                'learning_rate': lr_scheduler.get_last_lr()[0]
            }
            
            self.log_metrics(metrics, epoch + 1)
            
            # 打印日志
            if self.accelerator.is_main_process:
                print(f"Epoch {epoch+1}/{args.num_epochs} - "
                      f"Train Loss: {train_loss:.4f} - Val Loss: {val_loss:.4f} - "
                      f"LR: {lr_scheduler.get_last_lr()[0]:.2e}")
                print(f"Validation Recon: {val_recon:.4f} - KL: {val_kl:.4f}")
                
                if self.use_aim:
                    print(f"[Aim] Metrics tracked: train_loss={train_loss:.4f}, val_loss={val_loss:.4f}")
            
            # 早停检查
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                self.save_model(args.output_dir, is_best=True)
            else:
                self.patience_counter += 1
                
            # 定期保存
            if (epoch + 1) % args.save_every == 0:
                self.save_model(args.output_dir)
            
            # 早停
            if self.patience_counter >= args.patience:
                self.accelerator.print(f"Early stopping after {args.patience} epochs without improvement")
                break
        
        # 最终保存
        self.save_model(args.output_dir)
        
        if self.aim_run:
            self.aim_run.close()

def main():
    parser = argparse.ArgumentParser(description="Fine-tune Stable Diffusion VAE")
    
    # 模型和数据参数
    parser.add_argument("--model_name", type=str, default="runwayml/stable-diffusion-v1-5")
    parser.add_argument("--train_data_dir", type=str, required=True)
    parser.add_argument("--val_data_dir", type=str, required=True)
    parser.add_argument("--output_dir", type=str, default="./vae_finetuned")
    
    # 训练参数
    parser.add_argument("--batch_size", type=int, default=4)
    parser.add_argument("--num_epochs", type=int, default=20)
    parser.add_argument("--learning_rate", type=float, default=1e-5)
    parser.add_argument("--weight_decay", type=float, default=0.01)
    parser.add_argument("--image_size", type=int, default=512)
    
    # Accelerate参数
    parser.add_argument("--mixed_precision", type=str, default="fp16", choices=["no", "fp16", "bf16"])
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)
    
    # 早停参数
    parser.add_argument("--patience", type=int, default=5)
    parser.add_argument("--save_every", type=int, default=5)
    
    # Aim参数
    parser.add_argument("--use_aim", action="store_true", default=True)
    parser.add_argument("--experiment_name", type=str, default="vae_finetune")
    parser.add_argument("--aim_repo", type=str, default=".")
    
    args = parser.parse_args()
    
    # 初始化Accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
    )
    
    # 设置日志
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    
    # 准备数据
    transform = transforms.Compose([
        transforms.Resize((args.image_size, args.image_size)),
        transforms.ToTensor(),
        transforms.Normalize([0.5], [0.5])
    ])
    
    train_dataset = ImageDataset(args.train_data_dir, transform=transform)
    val_dataset = ImageDataset(args.val_data_dir, transform=transform)
    
    train_dataloader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        num_workers=4
    )
    val_dataloader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False, 
        num_workers=4
    )
    
    accelerator.print(f"Train dataset size: {len(train_dataset)}")
    accelerator.print(f"Validation dataset size: {len(val_dataset)}")
    
    # 初始化训练器
    tuner = VAETuner(
        model_name=args.model_name,
        accelerator=accelerator,
        use_aim=args.use_aim,
        experiment_name=args.experiment_name,
        aim_repo=args.aim_repo
    )
    
    # 开始训练
    tuner.train(train_dataloader, val_dataloader, args)

if __name__ == "__main__":
    main()