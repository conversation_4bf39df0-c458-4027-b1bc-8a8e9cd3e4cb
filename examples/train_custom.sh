#!/bin/bash

# Custom training example with specific configurations
# This example shows how to override various parameters

python scripts/train.py \
    model=sd21 \
    training=high_quality \
    data=custom \
    data.train_data_dir="/path/to/your/train/images" \
    data.val_data_dir="/path/to/your/val/images" \
    data.image_size=768 \
    training.num_epochs=30 \
    training.optimizer.learning_rate=5e-6 \
    model.loss_weights.kl_divergence=0.05 \
    experiment_name="custom_high_quality_training"
