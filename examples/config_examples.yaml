# Example configuration overrides for different use cases

# Example 1: Quick debugging setup
# python scripts/train.py --config-name=config_examples --config-path=examples +experiment=debug
defaults:
  - base_config

experiment: debug
training:
  num_epochs: 2
  batch_size: 2
data:
  image_size: 256

---
# Example 2: High-resolution training
# python scripts/train.py --config-name=config_examples --config-path=examples +experiment=high_res
defaults:
  - base_config

experiment: high_res
data:
  image_size: 1024
training:
  batch_size: 1
  gradient_accumulation_steps: 4

---
# Example 3: Memory-efficient training
# python scripts/train.py --config-name=config_examples --config-path=examples +experiment=memory_efficient
defaults:
  - base_config

experiment: memory_efficient
training:
  batch_size: 1
  gradient_accumulation_steps: 8
model:
  gradient_checkpointing: true
mixed_precision: fp16
