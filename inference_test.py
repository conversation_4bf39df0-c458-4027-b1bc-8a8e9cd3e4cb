import torch
from diffusers import StableDiffusionPipeline, AutoencoderKL
from PIL import Image
import argparse

def test_finetuned_vae(vae_path, base_model="runwayml/stable-diffusion-v1-5"):
    """测试微调后的VAE"""
    
    # 加载微调的VAE
    vae = AutoencoderKL.from_pretrained(vae_path)
    
    # 创建pipeline
    pipe = StableDiffusionPipeline.from_pretrained(
        base_model,
        vae=vae,
        torch_dtype=torch.float16,
        safety_checker=None,
        requires_safety_checker=False
    )
    pipe = pipe.to("cuda")
    
    # 测试生成
    prompt = "a beautiful landscape with mountains and lakes"
    
    with torch.no_grad():
        image = pipe(
            prompt,
            num_inference_steps=20,
            guidance_scale=7.5,
            height=512,
            width=512
        ).images[0]
    
    # 保存结果
    image.save("test_output.png")
    print(f"Generated image saved as test_output.png")
    
    return image

def test_vae_reconstruction(vae_path, test_image_path):
    """测试VAE重建能力"""
    from torchvision import transforms
    
    # 加载VAE
    vae = AutoencoderKL.from_pretrained(vae_path)
    vae = vae.to("cuda").half()
    
    # 准备图像
    transform = transforms.Compose([
        transforms.Resize((512, 512)),
        transforms.ToTensor(),
        transforms.Normalize([0.5], [0.5])
    ])
    
    image = Image.open(test_image_path).convert('RGB')
    input_tensor = transform(image).unsqueeze(0).to("cuda").half()
    
    # 编码-解码
    with torch.no_grad():
        latent = vae.encode(input_tensor).latent_dist.sample()
        reconstructed = vae.decode(latent).sample
    
    # 转换回图像
    reconstructed = (reconstructed / 2 + 0.5).clamp(0, 1)
    reconstructed = transforms.ToPILImage()(reconstructed.squeeze(0).cpu())
    
    # 保存结果
    reconstructed.save("reconstructed.png")
    print(f"Reconstructed image saved as reconstructed.png")
    
    return reconstructed

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--vae_path", type=str, required=True, help="Path to finetuned VAE")
    parser.add_argument("--test_type", type=str, choices=["generation", "reconstruction"], default="generation")
    parser.add_argument("--test_image", type=str, help="Test image for reconstruction")
    
    args = parser.parse_args()
    
    if args.test_type == "generation":
        test_finetuned_vae(args.vae_path)
    else:
        if not args.test_image:
            raise ValueError("--test_image is required for reconstruction test")
        test_vae_reconstruction(args.vae_path, args.test_image)