"""Setup script for VAE Tuner package."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="vaetuner",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A tool for fine-tuning Stable Diffusion VAE models",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/vaetuner",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "vae-train=scripts.train:main",
            "vae-inference=scripts.inference:main",
        ],
    },
)
