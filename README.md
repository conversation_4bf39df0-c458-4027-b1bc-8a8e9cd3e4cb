# Stable Diffusion VAE 微调系统

基于Hugging Face Diffusers、Accelerate和Aim的Stable Diffusion VAE微调系统。

## 功能特性

- ✅ 集成Hugging Face Accelerate库
- ✅ 支持单GPU/多GPU分布式训练  
- ✅ 支持FP16混合精度训练
- ✅ 集成Aim实验跟踪系统
- ✅ 实时监控训练和验证损失
- ✅ 早停机制和最佳模型保存
- ✅ 完整的训练-验证流程

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备数据

将训练和验证图片分别放在不同文件夹中：
```
data/
├── train/
│   ├── image1.jpg
│   ├── image2.png
│   └── ...
└── val/
    ├── val1.jpg
    ├── val2.png
    └── ...
```

### 2. 配置Accelerate

```bash
accelerate config
```

### 3. 开始训练

单GPU训练：
```bash
accelerate launch --mixed_precision=fp16 vae_tuner.py \
    --model_name "runwayml/stable-diffusion-v1-5" \
    --train_data_dir "./data/train" \
    --val_data_dir "./data/val" \
    --output_dir "./vae_finetuned" \
    --batch_size 4 \
    --num_epochs 20 \
    --learning_rate 1e-5 \
    --use_aim \
    --experiment_name "my_vae_experiment"
```

多GPU训练：
```bash
accelerate launch --multi_gpu --num_processes=2 --mixed_precision=fp16 vae_tuner.py \
    --model_name "runwayml/stable-diffusion-v1-5" \
    --train_data_dir "./data/train" \
    --val_data_dir "./data/val" \
    --output_dir "./vae_finetuned" \
    --batch_size 8 \
    --num_epochs 20 \
    --learning_rate 1e-5 \
    --use_aim \
    --experiment_name "my_vae_experiment_multi_gpu"
```

### 4. 监控训练

启动Aim UI：
```bash
aim up
```

在浏览器中访问 `http://localhost:43800` 查看训练进度。

### 5. 测试微调模型

生成测试：
```bash
python inference_test.py --vae_path "./vae_finetuned/best_vae" --test_type generation
```

重建测试：
```bash
python inference_test.py --vae_path "./vae_finetuned/best_vae" --test_type reconstruction --test_image "test.jpg"
```

## 参数说明

### 训练参数
- `--model_name`: 基础模型名称（默认: runwayml/stable-diffusion-v1-5）
- `--batch_size`: 批次大小（默认: 4）
- `--num_epochs`: 训练轮数（默认: 20）
- `--learning_rate`: 学习率（默认: 1e-5）
- `--weight_decay`: 权重衰减（默认: 0.01）

### Accelerate参数
- `--mixed_precision`: 混合精度类型（no/fp16/bf16，默认: fp16）
- `--gradient_accumulation_steps`: 梯度累积步数（默认: 1）

### 早停参数
- `--patience`: 早停耐心值（默认: 5）
- `--save_every`: 保存间隔（默认: 5）

### Aim参数
- `--use_aim`: 启用Aim监控
- `--experiment_name`: 实验名称
- `--aim_repo`: Aim仓库路径（默认: .）

## 输出结构

```
vae_finetuned/
├── vae/                 # 最新模型
│   ├── config.json
│   └── diffusion_pytorch_model.safetensors
└── best_vae/           # 最佳模型
    ├── config.json
    └── diffusion_pytorch_model.safetensors
```

## 监控指标

Aim会自动记录以下指标：
- `train_loss`: 训练损失
- `val_loss`: 验证损失  
- `train_recon_loss`: 训练重建损失
- `val_recon_loss`: 验证重建损失
- `train_kl_loss`: 训练KL散度损失
- `val_kl_loss`: 验证KL散度损失
- `learning_rate`: 学习率

## 性能优化

1. **内存优化**：使用FP16混合精度训练
2. **分布式训练**：支持多GPU并行训练
3. **梯度累积**：支持大批次训练
4. **早停机制**：避免过拟合
5. **梯度裁剪**：提高训练稳定性

## 故障排除

1. **CUDA内存不足**：减小batch_size或启用梯度累积
2. **训练不稳定**：降低学习率或调整损失权重
3. **Aim无法启动**：检查端口占用或更改端口