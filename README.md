# VAE Tuner

A modular tool for fine-tuning Stable Diffusion VAE models with Hydra configuration management.

## Features

- 🔧 **Modular Architecture**: Clean separation of models, data, training, and utilities
- ⚙️ **Hydra Configuration**: Flexible configuration management with composition
- 🚀 **Accelerate Integration**: Multi-GPU training support with mixed precision
- 📊 **Aim Logging**: Comprehensive experiment tracking
- 🎯 **Early Stopping**: Automatic training termination based on validation metrics
- 🔄 **Multiple Configurations**: Pre-defined configs for different training scenarios

## Project Structure

```
vaetuner/
├── configs/                    # Hydra configuration files
│   ├── config.yaml            # Main configuration
│   ├── model/                 # Model configurations
│   ├── training/              # Training configurations
│   ├── data/                  # Data configurations
│   └── experiment/            # Experiment configurations
├── src/vaetuner/              # Source code
│   ├── models/                # Model classes
│   ├── data/                  # Dataset and data utilities
│   ├── training/              # Training logic
│   └── utils/                 # Utility functions
├── scripts/                   # Entry point scripts
│   ├── train.py              # Training script
│   └── inference.py          # Inference script
└── requirements.txt
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd vaetuner
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install the package in development mode:
```bash
pip install -e .
```

## Quick Start

### Basic Training

1. **Prepare your data**: Organize your training and validation images in separate directories.

2. **Run training with default configuration**:
```bash
python scripts/train.py data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

### Configuration Examples

#### Using Different Models
```bash
# Train with Stable Diffusion 2.1
python scripts/train.py model=sd21 data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val

# Train with custom model
python scripts/train.py model.model_name=your/custom-model data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

#### Using Different Training Configurations
```bash
# Fast training (for debugging)
python scripts/train.py training=fast data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val

# High quality training
python scripts/train.py training=high_quality data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

#### Using Different Data Configurations
```bash
# Custom data configuration with stronger augmentation
python scripts/train.py data=custom data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val

# Custom image size
python scripts/train.py data.image_size=768 data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

#### Using Experiment Configurations
```bash
# Run baseline experiment
python scripts/train.py experiment=baseline data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val

# Run ablation study
python scripts/train.py experiment=ablation data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

### Advanced Configuration

#### Parameter Sweeps
```bash
# Sweep over different learning rates
python scripts/train.py -m training.optimizer.learning_rate=1e-5,5e-6,1e-6 data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val

# Sweep over different batch sizes and KL weights
python scripts/train.py -m training.batch_size=2,4,8 model.loss_weights.kl_divergence=0.05,0.1,0.2 data.train_data_dir=/path/to/train data.val_data_dir=/path/to/val
```

#### Custom Configuration Override
```bash
# Override any configuration parameter
python scripts/train.py \
    training.num_epochs=50 \
    training.optimizer.learning_rate=2e-5 \
    model.loss_weights.kl_divergence=0.05 \
    data.image_size=768 \
    data.train_data_dir=/path/to/train \
    data.val_data_dir=/path/to/val
```

## Monitoring Training

### Aim Integration

Start Aim UI to monitor training:
```bash
aim up
```

Visit `http://localhost:43800` in your browser to view training progress.

### Logged Metrics

The system automatically logs:
- Training and validation losses (total, reconstruction, KL divergence)
- Learning rate
- Hyperparameters
- Model checkpoints

## Inference

Test your fine-tuned model:
```bash
python scripts/inference.py output_dir=/path/to/trained/model
```

This will load the best model and perform reconstruction on a test image.

## Configuration Structure

### Model Configuration (`configs/model/`)
- `sd15.yaml`: Stable Diffusion 1.5 VAE
- `sd21.yaml`: Stable Diffusion 2.1 VAE
- Custom model configurations

### Training Configuration (`configs/training/`)
- `default.yaml`: Standard training settings
- `fast.yaml`: Quick training for debugging
- `high_quality.yaml`: Extended training for best results

### Data Configuration (`configs/data/`)
- `default.yaml`: Standard data processing
- `custom.yaml`: Custom augmentation and preprocessing

### Experiment Configuration (`configs/experiment/`)
- `baseline.yaml`: Baseline experiment setup
- `ablation.yaml`: Ablation study configuration

## Output Structure

```
outputs/
└── experiment_name/
    └── timestamp/
        ├── vae/                 # Latest model
        ├── best_vae/           # Best model
        ├── .hydra/             # Hydra configuration
        └── logs/               # Training logs
```

## Performance Optimization

1. **Memory Efficiency**: FP16 mixed precision training
2. **Distributed Training**: Multi-GPU support via Accelerate
3. **Gradient Accumulation**: Effective large batch training
4. **Early Stopping**: Prevents overfitting
5. **Gradient Clipping**: Improves training stability

## Troubleshooting

1. **CUDA Out of Memory**: Reduce `training.batch_size` or increase `training.gradient_accumulation_steps`
2. **Training Instability**: Lower `training.optimizer.learning_rate` or adjust `model.loss_weights`
3. **Configuration Errors**: Check required parameters marked with `???` in config files
4. **Import Errors**: Ensure package is installed with `pip install -e .`

## Migration from Old Version

If you have the old `vae_tuner.py` script, you can migrate by:

1. Moving your data paths to the new configuration system
2. Converting command-line arguments to Hydra overrides
3. Using the new modular training script

Example migration:
```bash
# Old way
python vae_tuner.py --train_data_dir /path/to/train --batch_size 8

# New way
python scripts/train.py data.train_data_dir=/path/to/train training.batch_size=8
```